#!/usr/bin/env python3
"""
Simple script to run SisaRasa locally
"""

import os
import sys

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("🚀 Starting SisaRasa Recipe Recommendation System...")
    print("📍 Running on http://localhost:5000")
    print("🔗 MongoDB URI configured from .env file")
    print("=" * 50)
    
    from api.app import app
    
    # Run the application
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False  # Disable reloader to avoid issues
    )
    
except Exception as e:
    print(f"❌ Error starting application: {e}")
    print("\nTroubleshooting tips:")
    print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
    print("2. Check that MongoDB connection is working")
    print("3. Verify that data/clean_recipes.json exists")
    sys.exit(1)
